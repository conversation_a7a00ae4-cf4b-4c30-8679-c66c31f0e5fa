//
//  IMYNASearchHotspotsView.m
//  ZZIMYMain
//
//  Created by ljh on 2023/8/7.
//

#import "IMYNASearchHotspotsView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYNASearchHotspotsRowCell : UIControl

@property (nonatomic, strong) UIImageView *rankView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *adLabel;
@property (nonatomic, strong) UIImageView *iconView;

@property (nonatomic, strong) IMYNASearchHotspotsKeyModel *keyModel;
@property (nonatomic, assign) NSInteger rowIndex; // 序号，从0开始

@end

@interface IMYNASearchHotspotsView ()

@property (nonatomic, strong) UIView *topBar;
@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) IMYCaptionView *loadingView;

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, assign) BOOL animated;

@end

@implementation IMYNASearchHotspotsView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.imy_width = SCREEN_WIDTH;
    [self setupTopbar];
    [self setupContent];
    self.name = @"搜索热点";
}

- (void)setName:(NSString *)name {
    if (!name.length) {
        return;
    }
    _name = [name copy];
    _titleLabel.text = _name;
}

// 内部布局复杂，先不用autolayout了
- (void)setupTopbar {
    // 移除 topBar，因为容器会管理标题
    // _topBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 44)];
    // [self addSubview:_topBar];
}





- (void)setupContent {
    _contentView = [[UIView alloc] initWithFrame:CGRectMake(12, 0, self.imy_width - 24, 0)];
    [self addSubview:_contentView];
    [_contentView imy_setBackgroundColorForKey:kCK_White_AN];
    [_contentView imy_drawAllCornerRadius:12];

    /// loading view 高度
    IMYCKLoadingView *ckLoading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingTodaySearchHotspots];
    _contentView.imy_height = ckLoading.imy_height;
    _loadingView = [IMYCaptionView addToView:_contentView];
    [_loadingView setStateView:ckLoading forState:IMYCaptionViewStateLoading];
    _loadingView.state = IMYCaptionViewStateLoading;

    self.imy_height = _contentView.imy_bottom + 12;
}

- (void)refreshHotspotsUI {
    // 判断是否无数据
    if (!_hotspotsKeys.count) {
        // 无数据
        self.alpha = 0;
        self.imy_height = 0;
        if (self.onHeightDidChangedBlock) {
            self.onHeightDidChangedBlock(self.animated);
        }
        return;
    }
    /// 清理旧UI
    NSMutableArray *reuseRowViews = [self.contentView.subviews mutableCopy];
    [self.contentView imy_removeAllSubviews];

    self.loadingView.state = IMYCaptionViewStateHidden;
    [reuseRowViews removeObject:self.loadingView];
    
    CGFloat currentY = 8;
    NSInteger rowIndex = 0;
    for (IMYNASearchHotspotsKeyModel *keyModel in _hotspotsKeys) {
        IMYNASearchHotspotsRowCell *cell = reuseRowViews.firstObject;
        if ([cell isKindOfClass:IMYNASearchHotspotsRowCell.class]) {
            [reuseRowViews removeObjectAtIndex:0];
        } else {
            cell = [IMYNASearchHotspotsRowCell new];
            [cell addTarget:self action:@selector(onItemPressedAction:) forControlEvents:UIControlEventTouchUpInside];
        }
        cell.frame = CGRectMake(0, currentY, _contentView.imy_width, 40);
        cell.rowIndex = rowIndex;
        cell.keyModel = keyModel;
        [_contentView addSubview:cell];
        
        cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"search-hots-%@-%ld", keyModel.show_word, keyModel.ad_model];
        cell.imyut_eventInfo.showRadius = 0.6;
        @weakify(self);
        cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self onExposuredWithKeyModel:keyModel];
        };
        
        currentY += cell.imy_height;
        rowIndex += 1;
    }
    
    _contentView.imy_height = currentY + 8;
    self.imy_height = _contentView.imy_bottom + 12;
    self.alpha = 1;

    if (self.onHeightDidChangedBlock) {
        self.onHeightDidChangedBlock(self.animated);
    }
}

- (void)onExposuredWithKeyModel:(IMYNASearchHotspotsKeyModel *)keyModel {
    if (self.onKeyDidExposuredBlock) {
        self.onKeyDidExposuredBlock(keyModel);
    }
}

- (void)setupWithHotspotsKeyModels:(NSArray<IMYNASearchHotspotsKeyModel *> *)keyModels
                          animated:(BOOL)animated {
    // 先清空属性，赋值index
    _hotspotsKeys = [keyModels copy];
    NSInteger all_index = 0;
    for (IMYNASearchHotspotsKeyModel *keyModel in _hotspotsKeys) {
        keyModel.index = all_index;
        all_index += 1;
    }

    if (animated) {
        [self runFadeAnimationBlock:^{
            [self refreshHotspotsUI];
        }];
    } else {
        [self refreshHotspotsUI];
    }
}

- (void)runFadeAnimationBlock:(void(^)(void))block {
    CATransition *anim = [CATransition new];
    anim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    anim.duration = 0.2;
    anim.fillMode = kCAFillModeForwards;
    anim.type = kCATransitionFade;
    self.animated = YES;
    block();
    self.animated = NO;
    [self.contentView.layer addAnimation:anim forKey:@"fade"];
}

- (void)onItemPressedAction:(UIButton *)sender {
    IMYNASearchHotspotsRowCell *rowCell = [sender imy_findParentViewWithClass:IMYNASearchHotspotsRowCell.class];
    // 搜索key被点击
    if (self.onKeyDidPressedBlock) {
        self.onKeyDidPressedBlock(rowCell.keyModel);
    }
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    if (self.onEmptyDidClickedBlock) {
        self.onEmptyDidClickedBlock();
    }
}





@end

@implementation IMYNASearchHotspotsRowCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    self.imy_height = 40;
    
    _rankView = [UIImageView new];
    _rankView.frame = CGRectMake(16, 0, 6, 6);
    [self addSubview:_rankView];
    
    _titleLabel = [UILabel new];
    _titleLabel.imy_height = self.imy_height;
    [_titleLabel imy_setTextColorForKey:kCK_Black_A];
    _titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    [self addSubview:_titleLabel];
    
    _iconView = [UIImageView new];
    _iconView.imy_size = CGSizeMake(16, 16);
    _iconView.hidden = YES;
    [self addSubview:_iconView];
    
    _adLabel = [UILabel new];
    _adLabel.textAlignment = NSTextAlignmentCenter;
    _adLabel.font = [UIFont systemFontOfSize:9 weight:UIFontWeightRegular];
    [_adLabel imy_setTextColorForKey:kCK_Black_J];
    _adLabel.hidden = YES;
    _adLabel.text = @"广告";
    [_adLabel imy_sizeToFit];
    [self addSubview:_adLabel];
    
    /// 全部垂直居中
    _rankView.imy_centerY = _iconView.imy_centerY = _adLabel.imy_centerY = _titleLabel.imy_centerY;
}

- (void)setKeyModel:(IMYNASearchHotspotsKeyModel *)keyModel {
    _keyModel = keyModel;
    [self refreshUI];
}

- (NSString *)rankImageNameWithIndex:(NSInteger)index {
    switch (index) {
        case 0:
            return @"pt_ellipse_red_6";
        case 1:
            return @"pt_ellipse_orange_6";
        case 2:
            return @"pt_ellipse_yellow_6";
    }
    return nil;
}

- (NSString *)iconNameWithType:(NSInteger)type {
    switch (type) {
        case 1:
            return @"pt_img_hot_red";
        case 2:
            return @"pt_img_recom_greed";
        case 3:
            return @"pt_img_new_purple";
        case 4:
            return @"pt_img_boil_orange";
        case 6:
            return @"pt_img_ai";
    }
    return nil;
}

- (void)refreshUI {
    if (_rowIndex < 3) {
        NSString *imageName = [self rankImageNameWithIndex:_rowIndex];
        _rankView.image = [UIImage imageNamed:imageName];
        [_rankView imy_drawAllCornerRadius:0];
        [_rankView imy_setBackgroundColorForKey:kCK_Clear_A];
    } else {
        _rankView.image = nil;
        [_rankView imy_drawAllCornerRadius:3];
        [_rankView imy_setBackgroundColorForKey:kCK_Black_J];
    }
    
    _titleLabel.text = _keyModel.show_word;
    [_titleLabel imy_sizeToFitWidth];
    _titleLabel.imy_left = _rankView.imy_right + 10;
    
    if (_keyModel.ad_model) {
        _iconView.hidden = NO;
        _adLabel.hidden = NO;
        
        if (imy_isNotEmptyString(_keyModel.ad_icon)) {
            [_iconView imy_setImageURL:[NSURL URLWithString:_keyModel.ad_icon]];
            _iconView.imy_size = CGSizeMake(16, 16);
        }
        
        CGFloat width = self.imy_width - 16 - _titleLabel.imy_left - 4;
        
        CGFloat adIconWidth = _iconView.imy_width + 4;
        if (imy_isEmptyString(_keyModel.ad_icon)) {
            _iconView.hidden = YES;
            adIconWidth = 0;
        }
        
        CGFloat adTagWidth = _adLabel.imy_width + 4;
        if (_keyModel.icon == 0) {
            _adLabel.hidden = YES;
            adTagWidth = 0;
        }
        
        CGFloat maxTitleWidth = width - adIconWidth - adTagWidth;
        
        if (_titleLabel.imy_width > maxTitleWidth) {
            _titleLabel.imy_width = maxTitleWidth;
        }
        
        if (!_iconView.isHidden) {
            if (_keyModel.ad_icon_pos == 1) { // icon靠前
                _iconView.imy_left = _titleLabel.imy_left;
                _titleLabel.imy_left = _iconView.imy_right + 4;
                _adLabel.imy_left = _titleLabel.imy_right + 4;
            } else { // icon靠后
                _iconView.imy_left = _titleLabel.imy_right + 4;
                _adLabel.imy_left = _iconView.imy_right + 4;
            }
        } else {
            _adLabel.imy_left = _titleLabel.imy_right + 10;
        }
    } else if (_keyModel.icon > 0 && _keyModel.icon <= 6) {
        /// 图标类型：0无，1热，2荐，3新，4沸，5广告
        if (_keyModel.icon != 5) {
            NSString *iconName = [self iconNameWithType:_keyModel.icon];
            _iconView.image = [UIImage imageNamed:iconName];
            _iconView.hidden = NO;
            _adLabel.hidden = YES;
        } else {
            _iconView.hidden = YES;
            _adLabel.hidden = NO;
        }
        
        /**
         (SCREEN_WIDTH - 24) / 2  , 父控件大小
         8 两个tag间距
         14 * 2 title左右边距
         4 title 跟 tag 的间距
         */
        CGFloat maxTitleWidth = self.imy_width - 16 - _titleLabel.imy_left - 4 - _adLabel.imy_width;
        if (_titleLabel.imy_width > maxTitleWidth) {
            _titleLabel.imy_width = maxTitleWidth;
        }
        _iconView.imy_left = _titleLabel.imy_right + 4;
        _adLabel.imy_left = _titleLabel.imy_right + 10;
    } else {
        _adLabel.hidden = YES;
        _iconView.hidden = YES;
        
        CGFloat maxTitleWidth = self.imy_width - 16 - _titleLabel.imy_left;
        if (_titleLabel.imy_width > maxTitleWidth) {
            _titleLabel.imy_width = maxTitleWidth;
        }
    }
}



@end

@implementation IMYNASearchHotspotsKeyModel

@end
